{"AWSTemplateFormatVersion": "2010-09-09", "Parameters": {"PerformanceVPCStack": {"Description": "Stack name of VPC", "Type": "String"}, "ClusterMasterUsername": {"Description": "The master username for the DB instance", "Type": "String", "Default": "performancemasteruser"}, "ClusterClientUsername": {"Description": "The client username for the DB instance", "Type": "String", "Default": "performanceclientuser"}, "V2ClusterClientUserSecretname": {"Description": "The client username for the DB instance", "Type": "String", "Default": "performanceclusterv2clientsecret"}, "DatabaseUserCreatorLambdaDeploy": {"Type": "String", "Default": "dummy.zip", "Description": "S3Key to load the lambda function from."}, "DatabaseName": {"Type": "String", "Default": "performance", "Description": "Name of the database created in the Cluster"}, "Env": {"Description": "Environment type", "Type": "String", "AllowedValues": ["dev", "test", "int", "demo", "prod"]}}, "Conditions": {"isProd": {"Fn::Equals": [{"Ref": "Env"}, "prod"]}}, "Resources": {"ClusterSecurityGroup": {"Type": "AWS::EC2::SecurityGroup", "Properties": {"GroupDescription": {"Ref": "AWS::StackName"}, "SecurityGroupIngress": [{"IpProtocol": "tcp", "FromPort": 5432, "ToPort": 5432, "SourceSecurityGroupId": {"Fn::ImportValue": {"Fn::Sub": "${PerformanceVPCStack}-ClientSecurityGroup"}}}], "VpcId": {"Fn::ImportValue": {"Fn::Sub": "${PerformanceVPCStack}-VPC"}}}}, "ClusterSubnetGroup": {"Type": "AWS::RDS::DBSubnetGroup", "Properties": {"DBSubnetGroupDescription": {"Ref": "AWS::StackName"}, "SubnetIds": {"Fn::Split": [",", {"Fn::ImportValue": {"Fn::Sub": "${PerformanceVPCStack}-SubnetsPrivate"}}]}}}, "ClusterEncryptionKey": {"Type": "AWS::KMS::Key", "Properties": {"EnableKeyRotation": true, "KeyPolicy": {"Version": "2012-10-17", "Statement": [{"Sid": "AdminAccess", "Effect": "Allow", "Principal": {"AWS": {"Fn::Sub": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/ReadyKit_Admin"}}, "Action": "kms:*", "Resource": "*"}, {"Sid": "JenkinsAccess", "Effect": "Allow", "Principal": {"AWS": {"Fn::Sub": "arn:${AWS::Partition}:iam::${AWS::AccountId}:user/rk-ci-jenkins-user"}}, "Action": "kms:*", "Resource": "*"}, {"Sid": "CustomUserCreatorAccess", "Effect": "Allow", "Principal": {"AWS": {"Fn::GetAtt": ["DatabaseUserCreatorLambdaRole", "<PERSON><PERSON>"]}}, "Action": ["kms:Decrypt", "kms:GenerateDataKey"], "Resource": "*"}, {"Sid": "enable root access and prevent permission delegation", "Effect": "Allow", "Principal": {"AWS": {"Fn::Sub": "arn:aws:iam::${AWS::AccountId}:root"}}, "Action": "kms:*", "Resource": "*", "Condition": {"StringEquals": {"aws:PrincipalType": "Account"}}}, {"Sid": "allow all IAM entities from same account to read the CMK configuration", "Effect": "Allow", "Principal": {"AWS": {"Fn::Sub": "arn:aws:iam::${AWS::AccountId}:root"}}, "Action": ["kms:DescribeCustomKeyStores", "kms:DescribeKey", "kms:GetKeyPolicy", "kms:GetKeyRotationStatus", "kms:GetParametersForImport", "kms:GetPublicKey", "kms:ListAliases", "kms:ListGrants", "kms:ListKeyPolicies", "kms:ListKeys", "kms:ListResourceTags", "kms:ListRetirableGrants"], "Resource": "*"}, {"Sid": "Admin access", "Effect": "Allow", "Principal": {"AWS": {"Fn::Sub": "arn:aws:iam::${AWS::AccountId}:root"}}, "Action": ["kms:CancelKeyDeletion", "kms:ConnectCustomKeyStore", "kms:<PERSON><PERSON><PERSON><PERSON><PERSON>", "kms:CreateCustomKeyStore", "kms:DeleteAlias", "kms:DeleteCustomKeyStore", "kms:DeleteImportedKeyMaterial", "kms:Disable<PERSON><PERSON>", "kms:DisableKeyRotation", "kms:EnableKey", "kms:EnableKeyRotation", "kms:PutKeyPolicy", "kms:Update<PERSON><PERSON><PERSON>", "kms:UpdateCustomKeyStore", "kms:UpdateKeyDescription", "kms:UpdatePrimaryRegion"], "Resource": "*", "Condition": {"ArnLike": {"aws:PrincipalArn": [{"Fn::Sub": "arn:aws:iam::${AWS::AccountId}:role/aws-reserved/sso.amazonaws.com/eu-central-1/AWSReservedSSO_Syskron_SSO_KMS_Ops_*"}, {"Fn::Sub": "arn:aws:iam::${AWS::AccountId}:role/cdk-*-cfn-exec-role-${AWS::AccountId}-eu-central-1"}]}}}, {"Sid": "Read only access", "Effect": "Allow", "Principal": {"AWS": {"Fn::Join": ["", ["arn:aws:iam::", {"Ref": "AWS::AccountId"}, ":root"]]}}, "Action": ["kms:Decrypt", "kms:DescribeKey"], "Resource": "*", "Condition": {"ArnLike": {"aws:PrincipalArn": [{"Fn::Sub": "arn:aws:iam::${AWS::AccountId}:role/aws-reserved/sso.amazonaws.com/eu-central-1/AWSReservedSSO_Syskron_SSO_Developer_RO_*"}, {"Fn::Sub": "arn:aws:iam::${AWS::AccountId}:role/aws-reserved/sso.amazonaws.com/eu-central-1/AWSReservedSSO_Syskron_SSO_DevOps_RO_*"}]}}}, {"Sid": "Read and Write access", "Effect": "Allow", "Principal": {"AWS": {"Fn::Sub": "arn:aws:iam::${AWS::AccountId}:root"}}, "Action": ["kms:Decrypt", "kms:Encrypt", "kms:DescribeKey", "kms:GenerateDataKey", "kms:GenerateDataKeyPair", "kms:GenerateDataKeyPairWithoutPlaintext", "kms:GenerateDataKeyWithoutPlaintext", "kms:ReEncryptFrom", "kms:ReEncryptTo"], "Resource": "*", "Condition": {"ArnLike": {"aws:PrincipalArn": [{"Fn::Sub": "arn:aws:iam::${AWS::AccountId}:role/aws-reserved/sso.amazonaws.com/eu-central-1/AWSReservedSSO_Syskron_SSO_Developer_RW_*"}, {"Fn::Sub": "arn:aws:iam::${AWS::AccountId}:role/aws-reserved/sso.amazonaws.com/eu-central-1/AWSReservedSSO_Syskron_SSO_DevOps_RW_*"}]}}}, {"Sid": "All service access", "Effect": "Allow", "Principal": {"AWS": "*"}, "Action": ["kms:CreateGrant", "kms:Decrypt", "kms:DescribeKey", "kms:Encrypt", "kms:GenerateDataKey", "kms:GenerateDataKeyPair", "kms:GenerateDataKeyPairWithoutPlaintext", "kms:GenerateDataKeyWithoutPlaintext", "kms:ListGrants", "kms:ReEncryptFrom", "kms:ReEncryptTo"], "Resource": "*", "Condition": {"StringEquals": {"kms:CallerAccount": {"Ref": "AWS::AccountId"}}, "StringLike": {"kms:ViaService": "*.eu-central-1.amazonaws.com"}}}, {"Sid": "Cloud watch log group access", "Effect": "Allow", "Principal": {"Service": "logs.eu-central-1.amazonaws.com"}, "Action": ["kms:CreateGrant", "kms:Decrypt", "kms:DescribeKey", "kms:Encrypt", "kms:GenerateDataKey", "kms:GenerateDataKeyPair", "kms:GenerateDataKeyPairWithoutPlaintext", "kms:GenerateDataKeyWithoutPlaintext", "kms:ListGrants", "kms:ReEncryptFrom", "kms:ReEncryptTo"], "Resource": "*", "Condition": {"ArnLike": {"kms:EncryptionContext:aws:logs:arn": {"Fn::Sub": "arn:aws:logs:eu-central-1:${AWS::AccountId}:*"}}}}]}}}, "V2ClusterParameterGroup": {"Type": "AWS::RDS::DBClusterParameterGroup", "Properties": {"Description": {"Ref": "AWS::StackName"}, "Family": "aurora-postgresql16", "Parameters": {"client_encoding": "UTF8"}}}, "V2ClusterMasterSecret": {"Type": "AWS::<PERSON>Manager::Secret", "Properties": {"KmsKeyId": {"Ref": "ClusterEncryptionKey"}, "GenerateSecretString": {"SecretStringTemplate": {"Fn::Join": ["", ["{\"username\": \"", {"Ref": "ClusterMasterUsername"}, "\"}"]]}, "GenerateStringKey": "password", "PasswordLength": 16, "ExcludeCharacters": "\"@/\\'%"}}}, "V2RdsServerlessCluster": {"DeletionPolicy": "Snapshot", "UpdateReplacePolicy": "Snapshot", "Type": "AWS::RDS::DBCluster", "Properties": {"MasterUsername": "performancemasteruser", "MasterUserPassword": {"Fn::Join": ["", ["{{resolve:secretsmanager:", {"Ref": "V2ClusterMasterSecret"}, ":SecretString:password}}"]]}, "DatabaseName": {"Ref": "DatabaseName"}, "Engine": "aurora-postgresql", "EngineVersion": "16.6", "Port": 5432, "ServerlessV2ScalingConfiguration": {"MaxCapacity": {"Fn::If": ["is<PERSON><PERSON>", 64, 16]}, "MinCapacity": {"Fn::If": ["is<PERSON><PERSON>", 1, 0.5]}}, "DBClusterParameterGroupName": {"Ref": "V2ClusterParameterGroup"}, "DBSubnetGroupName": {"Ref": "ClusterSubnetGroup"}, "BackupRetentionPeriod": 30, "StorageEncrypted": true, "KmsKeyId": {"Ref": "ClusterEncryptionKey"}, "DeletionProtection": true, "VpcSecurityGroupIds": [{"Ref": "ClusterSecurityGroup"}], "EnableHttpEndpoint": true, "CopyTagsToSnapshot": true}}, "V2RdsServerlessInstance": {"Type": "AWS::RDS::DBInstance", "Properties": {"Engine": "aurora-postgresql", "DBInstanceClass": "db.serverless", "DBClusterIdentifier": {"Ref": "V2RdsServerlessCluster"}}}, "V2DatabaseUserCreatorLambda": {"Type": "AWS::Lambda::Function", "Properties": {"Role": {"Fn::GetAtt": ["DatabaseUserCreatorLambdaRole", "<PERSON><PERSON>"]}, "Tags": [{"Key": "SERVICE", "Value": "s2a-performance"}], "Runtime": "python3.13", "Architectures": ["arm64"], "Timeout": 600, "MemorySize": 2048, "Code": {"S3Bucket": {"Fn::Join": ["", ["rk-deploy-jenkins-", {"Fn::ImportValue": "rk-footprint:stage"}]]}, "S3Key": {"Ref": "DatabaseUserCreatorLambdaDeploy"}}, "Handler": "lambda_function.lambda_handler", "Environment": {"Variables": {"MASTER_SECRET_ARN": {"Ref": "V2ClusterMasterSecret"}, "CLUSTER_ARN": {"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":rds:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":cluster:", {"Ref": "V2RdsServerlessCluster"}]]}, "DB_NAME": {"Ref": "DatabaseName"}, "DB_ENGINE": "postgres", "DB_HOST": {"Fn::GetAtt": ["V2RdsServerlessCluster", "Endpoint.Address"]}, "DB_PORT": {"Fn::GetAtt": ["V2RdsServerlessCluster", "Endpoint.Port"]}, "DB_CLUSTER_IDENTIFIER": {"Ref": "V2RdsServerlessCluster"}}}, "VpcConfig": {"SecurityGroupIds": [{"Fn::ImportValue": {"Fn::Sub": "${PerformanceVPCStack}-ClientSecurityGroup"}}], "SubnetIds": {"Fn::Split": [",", {"Fn::ImportValue": {"Fn::Sub": "${PerformanceVPCStack}-SubnetsPrivate"}}]}}}}, "DatabaseUserCreatorSecretManagerPolicy": {"Type": "AWS::IAM::ManagedPolicy", "Properties": {"PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["secretsmanager:<PERSON><PERSON><PERSON><PERSON><PERSON>", "secretsmanager:TagResource", "secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:DeleteSecret", "secretsmanager:UpdateSecret", "secretsmanager:GetSecretValue", "secretsmanager:GetRandomPassword"], "Resource": "*"}]}}}, "DatabaseUserCreatorKmsPolicy": {"Type": "AWS::IAM::ManagedPolicy", "Properties": {"PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["kms:Decrypt", "kms:GenerateDataKey"], "Resource": "*"}]}}}, "DatabaseUserCreatorRdsPolicy": {"Type": "AWS::IAM::ManagedPolicy", "Properties": {"PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["rds-data:ExecuteStatement"], "Resource": "*"}]}}}, "DatabaseUserCreatorLambdaRole": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}, "Action": "sts:<PERSON><PERSON>Role"}]}, "ManagedPolicyArns": ["arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole", "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole", {"Ref": "DatabaseUserCreatorKmsPolicy"}, {"Ref": "DatabaseUserCreatorSecretManagerPolicy"}, {"Ref": "DatabaseUserCreatorRdsPolicy"}]}}, "V2ClientDatabaseUser": {"Type": "Custom::V2DatabaseUser", "DependsOn": "V2RdsServerlessInstance", "Properties": {"ServiceToken": {"Fn::GetAtt": ["V2DatabaseUserCreatorLambda", "<PERSON><PERSON>"]}, "Username": {"Ref": "ClusterClientUsername"}, "SecretName": {"Ref": "V2ClusterClientUserSecretname"}}}}, "Outputs": {"StackName": {"Description": "Stack name.", "Value": {"Fn::Sub": "${AWS::StackName}"}}, "V2ClusterName": {"Description": "The name of the cluster.", "Value": {"Ref": "V2RdsServerlessCluster"}, "Export": {"Name": {"Fn::Sub": "${AWS::StackName}-V2ClusterName"}}}, "DatabaseName": {"Description": "Name of the database in the DB cluster.", "Value": {"Ref": "DatabaseName"}, "Export": {"Name": {"Fn::Sub": "${AWS::StackName}-DatabaseName"}}}, "V2DNSName": {"Description": "The connection endpoint for the DB cluster.", "Value": {"Fn::GetAtt": ["V2RdsServerlessCluster", "Endpoint.Address"]}, "Export": {"Name": {"Fn::Sub": "${AWS::StackName}-V2DNSName"}}}, "V2ClusterPort": {"Description": "The connection port for the DB cluster.", "Value": {"Fn::GetAtt": ["V2RdsServerlessCluster", "Endpoint.Port"]}, "Export": {"Name": {"Fn::Sub": "${AWS::StackName}-V2ClusterPort"}}}, "V2ClientDatabaseUserSecretArn": {"Description": "Arn of secret that contains user credentials for accessing the database.", "Value": {"Fn::GetAtt": ["V2ClientDatabaseUser", "SecretArn"]}, "Export": {"Name": {"Fn::Sub": "${AWS::StackName}-V2ClientDatabaseUserSecretArn"}}}, "V2MasterSecretArn": {"Description": "Arn of secret that contains user credentials for master user of the database.", "Value": {"Ref": "V2ClusterMasterSecret"}, "Export": {"Name": {"Fn::Sub": "${AWS::StackName}-V2MasterSecretArn"}}}, "DatabaseUserCreatorLambdaRoleARN": {"Description": "Arn of role for accessing the database as master.", "Value": {"Fn::GetAtt": ["DatabaseUserCreatorLambdaRole", "<PERSON><PERSON>"]}, "Export": {"Name": {"Fn::Sub": "${AWS::StackName}-DatabaseUserCreatorLambdaRoleARN"}}}, "SecurityGroupId": {"Description": "The security group used to manage access to RDS Aurora Serverless Postgres.", "Value": {"Ref": "ClusterSecurityGroup"}, "Export": {"Name": {"Fn::Sub": "${AWS::StackName}-SecurityGroupId"}}}}}