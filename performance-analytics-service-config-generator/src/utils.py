import json
import os
from typing import Any

from aws_lambda_powertools import Logger
import boto3

LOGGER = Logger()


def get_timezone(request_payload: Any, cache: dict | None = None) -> type[StopIteration] | str:
    role_arn = os.environ["S2A_EQUIPMENTS_SERVICE_CROSS_ACCOUNT_ACCESS_ROLE_ANCESTORS_ARN"]
    LOGGER.debug('Invoke equipment: Assume IAM role ARN="%s".', role_arn)
    sts_client = boto3.client("sts")
    # by default the temporary security credentials created by AssumeRole last for one hour
    if not cache:
        cache = {}
        role = sts_client.assume_role(RoleArn=role_arn, RoleSessionName="readykit")
        cache["assumed_role"] = role
    else:
        role = cache["assumed_role"]

    LOGGER.debug("Invoke equipment get: Create lambda client with role.")
    lambda_client = boto3.client(
        "lambda",
        aws_access_key_id=role["Credentials"]["AccessKeyId"],
        aws_secret_access_key=role["Credentials"]["SecretAccessKey"],
        aws_session_token=role["Credentials"]["SessionToken"],
    )

    function_name = os.environ["S2A_EQUIPMENTS_SERVICE_LAMBDA_ARN"]
    LOGGER.debug('Invoke equipment BE Get: Invoke lambda ARN="%s".', function_name)
    response = lambda_client.invoke(
        FunctionName=function_name,
        InvocationType="RequestResponse",
        Payload=json.dumps(request_payload),
    )

    timezone = ""
    response_byte_array = response["Payload"].read()
    response_dict = json.loads(response_byte_array)
    LOGGER.debug('Invoke equipment BE Get: Response from lambda is "%s".', response_dict)
    for ancestor in response_dict:
        if isinstance(ancestor, dict):
            if ancestor.get("level", "") == "plant":
                LOGGER.debug('Found plant= "%s"', ancestor.get("techDesc", "No Name provided"))
                properties = ancestor.get("properties", {})
                try:
                    timezone = next(prop["propertyValue"] for prop in properties if prop["propertyName"] == "timezone")
                except StopIteration:
                    LOGGER.error("There was no timezone found on the parent plant of this line")
                    return StopIteration
        else:
            LOGGER.error("Invalid response from equipment service: %s", response_dict)
            return StopIteration

    return timezone


def invoke_cross_account_lambda(function_name: str, request_payload: Any, cache: dict | None = None) -> Any:
    role_arn = os.environ["S2A_SHIFT_SERVICE_CROSS_ACCOUNT_ACCESS_ROLE_ARN"]
    LOGGER.debug('Invoke shift: Assume IAM role ARN="%s".', role_arn)
    sts_client = boto3.client("sts")
    # by default the temporary security credentials created by AssumeRole last for one hour
    if not cache:
        cache = {}
        role = sts_client.assume_role(RoleArn=role_arn, RoleSessionName="readykit")
        cache["assumed_role"] = role
    else:
        role = cache["assumed_role"]

    LOGGER.debug("Invoke shift BE: Create lambda client with role.")
    lambda_client = boto3.client(
        "lambda",
        aws_access_key_id=role["Credentials"]["AccessKeyId"],
        aws_secret_access_key=role["Credentials"]["SecretAccessKey"],
        aws_session_token=role["Credentials"]["SessionToken"],
    )

    LOGGER.debug('Invoke shift BE: Invoke lambda ARN="%s".', function_name)
    response = lambda_client.invoke(
        FunctionName=function_name,
        InvocationType="RequestResponse",
        Payload=json.dumps(request_payload),
    )

    return response
