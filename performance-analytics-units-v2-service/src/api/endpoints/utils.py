
from http import HTTPStatus
from aws_lambda_powertools import Logger
from fastapi import HTTPException
from performance_analytics.utility.block_counters import get_block_level_counters, query_counters
from performance_analytics.models.block_configuration import BlockCounterStatus
from lib_equipment_cache_common.clients.equipment_client import EquipmentClient
from api.api_schemas.models import CounterReportResponse

LOGGER = Logger()




# pylint: disable=too-many-positional-arguments
def get_block_counter_report(
    base_response: dict, line_id: str, account: str,  time_from: int, time_to: int
) -> CounterReportResponse:
    """Get block counters.

    Args:
        base_response: Base response dictionary with common fields
        line_id: line identifier
        account: Customer account
        time_from: Start timestamp
        time_to: End timestamp

    Returns:
        CounterReportResponse: The block counter report response.

    Raises:
        HTTPException: If an error occurs during report retrieval.

    """
    block_counter_config = get_block_level_counters(
        account=account, line_id=line_id, time_from=time_from, time_to=time_to
    )

    response = base_response.copy()

    if block_counter_config.status in (
        BlockCounterStatus.SUCCESS,
        BlockCounterStatus.OVERLAPPING_TIMERANGE_FOR_BLOCK_COUNTER,
    ):
        start_time = time_from
        if (
            block_counter_config.status
            == BlockCounterStatus.OVERLAPPING_TIMERANGE_FOR_BLOCK_COUNTER
        ):
            start_time = block_counter_config.block_valid_from
        try:
            equipment_cache = EquipmentClient(account)
            lead_machine = equipment_cache.get_lead_machine_by_line_id(line_id)

            units_map = query_counters(
                account=account,
                machine_id=lead_machine,
                time_from=start_time,
                time_to=time_to,
                calculate_block_counters=True,
            )
        except HTTPException as e:
            raise HTTPException(
                status_code=e.status_code if hasattr(e, "status_code")
                else HTTPStatus.INTERNAL_SERVER_ERROR,
                detail=f"Error querying block counters: {str(e)}"
            ) from e
        # pylint: disable=broad-except
        except Exception as e:
            raise HTTPException(
                status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
                detail=f"Error getting lead machine: {str(e)}"
            ) from e
        for unit_type in ["units_produced", "units_defect", "units_total"]:
            response[unit_type] = units_map.get(unit_type)
        if (
            block_counter_config.status
            == BlockCounterStatus.OVERLAPPING_TIMERANGE_FOR_BLOCK_COUNTER
        ):
            response["time_from"] = start_time
            response["eq_id"] = line_id
    else:
        raise HTTPException(
            status_code=HTTPStatus.NOT_FOUND,
            detail=(
                f"Block level configuration not found for account: {account} "
                f"and line_id: {line_id} for given timerange"
            ),
        )

    return CounterReportResponse(**response)


def get_machine_counter_report(
    base_response: dict, machine_id: str, account: str, time_from: int, time_to: int
) -> CounterReportResponse:
    """Get machine counters.

    Args:
        base_response: Base response dictionary with common fields
        machine_id: Machine identifier
        account: Customer account
        time_from: Start timestamp
        time_to: End timestamp

    Returns:
        CounterReportResponse: The machine counter report response.

    Raises:
        HTTPException: If an error occurs during report retrieval.

    """
    response = base_response.copy()

    try:
        units_map = query_counters(account, machine_id, time_from, time_to)

        response.update(
            {
                "units_produced": round(units_map["units_produced"]),
                "units_defect": round(units_map["units_defect"]),
                "units_total": round(units_map["units_total"]),
            }
        )

        # Add station_counters if they exist
        if "station_counters" in units_map:
            response["station_counters"] = units_map["station_counters"]

    except HTTPException as e:
        LOGGER.debug("HTTP error getting counter data for machine_id=%s: %s", machine_id, str(e))
        # Set default values for units in case of error
        response.update({"units_produced": 0, "units_defect": 0, "units_total": 0})

    return CounterReportResponse(**response)
