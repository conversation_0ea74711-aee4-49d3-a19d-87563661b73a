

from typing import Any
from pydantic import BaseModel, ConfigDict, Field, field_validator
from pydantic.alias_generators import to_camel


# Use camel case for request response to adhere to OpenAPI specifications
# But keep snake case to adhere to PEP8
class ApiModel(BaseModel):
    """Base model for API responses with consistent configuration."""
    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True,
        from_attributes=True,
        extra="allow"
    )


class CounterReportResponse(ApiModel):
    """Response model for counter report endpoint."""
    customer: str = Field(..., description="Customer account identifier")
    eq_id: str = Field(..., alias="eqId", description="Equipment ID (machine or line ID)")
    time_from: int = Field(..., alias="timeFrom", description="Start timestamp in milliseconds")
    time_to: int = Field(..., alias="timeTo", description="End timestamp in milliseconds")
    units_produced: int = Field(
        default=0,
        alias="unitsProduced",
        description="Number of units produced"
    )
    units_defect: int = Field(
        default=0,
        alias="unitsDefect",
        description="Number of defective units"
    )
    units_total: int = Field(default=0, alias="unitsTotal", description="Total number of units")
    station_counters: dict[str, Any] | None = Field(
        default=None, alias="stationCounters", description="Station-specific counter data"
    )

    class Config:
        validate_assignment = False



    @field_validator("station_counters", mode="before")
    @classmethod
    def ensure_station_counters_is_dict(cls, value: Any) -> dict[str, Any] | None:  # noqa: N805
        """Ensure station_counters is a valid dictionary or None."""
        if value is None:
            return None
        if not isinstance(value, dict):
            return {}
        return value
