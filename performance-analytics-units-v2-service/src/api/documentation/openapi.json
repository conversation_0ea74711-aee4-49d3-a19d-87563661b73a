{"openapi": "3.1.0", "info": {"title": "Performance Analytics Counter Report Service API", "description": "API for Counter Report", "version": "1.0.0"}, "paths": {"/v1/performance-analytics/units-report/v2/{line_id}": {"get": {"tags": ["counter-report"], "summary": "Block Counter Report", "description": "Retrieve the block counter report for a specific line within a given time range.\n\nArgs:\n    request (Request): The FastAPI request object.\n    properties (Share2ActProperties): Properties for the request.\n    line_id (str): Line identifier (from path).\n    time_from (int): Start of the time range (unix epoch in milliseconds, from query).\n    time_to (Optional[int]): End of the time range (unix epoch in milliseconds, from query).\n\nReturns:\n    CounterReportResponse: The block counter report response.\n\nRaises:\n    HTTPException: If an error occurs during report retrieval.", "operationId": "block_counter_report", "parameters": [{"name": "line_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Line identifier", "title": "Line Id"}, "description": "Line identifier"}, {"name": "time_from", "in": "query", "required": true, "schema": {"type": "integer", "description": "unix epoch timestamp in milliseconds", "title": "Time From"}, "description": "unix epoch timestamp in milliseconds"}, {"name": "time_to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "unix epoch timestamp in milliseconds ", "title": "Time To"}, "description": "unix epoch timestamp in milliseconds "}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CounterReportResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/performance-analytics/units-report/v2/{line_id}/{machine_id}": {"get": {"tags": ["counter-report"], "summary": "Machine Counter Report", "description": "Retrieve the machine counter report for a specific machine within a given time range.\n\nArgs:\n    request (Request): The FastAPI request object.\n    properties (Share2ActProperties): Properties for the request.\n    line_id (str): Line identifier (from path).\n    machine_id (str): Machine identifier (from path).\n    time_from (int): Start of the time range (unix epoch in milliseconds, from query).\n    time_to (Optional[int]): End of the time range (unix epoch in milliseconds, from query).\n\nReturns:\n    CounterReportResponse: The machine counter report response.\n\nRaises:\n    HTTPException: If an error occurs during report retrieval.", "operationId": "machine_counter_report", "parameters": [{"name": "line_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Line identifier", "title": "Line Id"}, "description": "Line identifier"}, {"name": "machine_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Machine identifier", "title": "Machine Id"}, "description": "Machine identifier"}, {"name": "time_from", "in": "query", "required": true, "schema": {"type": "integer", "description": "unix epoch timestamp in milliseconds", "title": "Time From"}, "description": "unix epoch timestamp in milliseconds"}, {"name": "time_to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "unix epoch timestamp in milliseconds", "title": "Time To"}, "description": "unix epoch timestamp in milliseconds"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CounterReportResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"CounterReportResponse": {"properties": {"customer": {"type": "string", "title": "Customer", "description": "Customer account identifier"}, "eqId": {"type": "string", "title": "Eqid", "description": "Equipment ID (machine or line ID)"}, "timeFrom": {"type": "integer", "title": "Timefrom", "description": "Start timestamp in milliseconds"}, "timeTo": {"type": "integer", "title": "Timeto", "description": "End timestamp in milliseconds"}, "unitsProduced": {"type": "integer", "title": "Unitsproduced", "description": "Number of units produced", "default": 0}, "unitsDefect": {"type": "integer", "title": "Unitsdefect", "description": "Number of defective units", "default": 0}, "unitsTotal": {"type": "integer", "title": "Unitstotal", "description": "Total number of units", "default": 0}, "stationCounters": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Stationcounters", "description": "Station-specific counter data"}}, "additionalProperties": true, "type": "object", "required": ["customer", "eqId", "timeFrom", "timeTo"], "title": "CounterReportResponse", "description": "Response model for counter report endpoint."}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}, "tags": [{"name": "units-report", "description": "Units report"}]}